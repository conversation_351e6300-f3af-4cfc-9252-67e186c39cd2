"""
Intelligent prompt generation for web design images.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ImageType(Enum):
    """Types of images for web design."""
    HERO = "hero"
    PRODUCT = "product"
    BACKGROUND = "background"
    ICON = "icon"
    BANNER = "banner"
    THUMBNAIL = "thumbnail"
    AVATAR = "avatar"
    LOGO = "logo"
    ILLUSTRATION = "illustration"
    PHOTO = "photo"


class WebsiteCategory(Enum):
    """Categories of websites."""
    BUSINESS = "business"
    ECOMMERCE = "ecommerce"
    BLOG = "blog"
    PORTFOLIO = "portfolio"
    LANDING = "landing"
    CORPORATE = "corporate"
    CREATIVE = "creative"
    TECH = "tech"
    HEALTH = "health"
    EDUCATION = "education"


@dataclass
class ImageContext:
    """Context information for image generation."""
    image_type: ImageType
    website_category: WebsiteCategory
    content_description: str
    style_keywords: List[str]
    color_scheme: Optional[List[str]] = None
    target_audience: Optional[str] = None
    brand_attributes: Optional[List[str]] = None
    technical_specs: Optional[Dict[str, Any]] = None


@dataclass
class PromptTemplate:
    """Template for generating prompts."""
    base_prompt: str
    style_modifiers: List[str]
    quality_keywords: List[str]
    technical_specs: List[str]


class PromptGenerator:
    """Generates contextually appropriate prompts for image generation."""
    
    def __init__(self):
        """Initialize the prompt generator with templates and keywords."""
        self.templates = self._load_templates()
        self.style_keywords = self._load_style_keywords()
        self.quality_modifiers = self._load_quality_modifiers()
        logger.info("Prompt generator initialized")
    
    def generate_prompt(
        self, 
        context: ImageContext, 
        custom_prompt: Optional[str] = None
    ) -> str:
        """Generate a prompt based on context.
        
        Args:
            context: Image context information
            custom_prompt: Optional custom prompt to enhance
            
        Returns:
            Generated prompt optimized for FLUX model
        """
        if custom_prompt:
            # Enhance existing prompt
            return self._enhance_prompt(custom_prompt, context)
        else:
            # Generate from template
            return self._generate_from_template(context)
    
    def _generate_from_template(self, context: ImageContext) -> str:
        """Generate prompt from template based on context."""
        # Get base template
        template = self.templates.get(
            (context.image_type, context.website_category),
            self.templates.get(context.image_type, self._get_default_template())
        )
        
        # Build prompt components
        components = []
        
        # Base description
        if context.content_description:
            components.append(context.content_description)
        else:
            components.append(template.base_prompt)
        
        # Add style modifiers
        style_mods = self._select_style_modifiers(context)
        if style_mods:
            components.extend(style_mods)
        
        # Add color scheme
        if context.color_scheme:
            color_desc = f"color palette: {', '.join(context.color_scheme)}"
            components.append(color_desc)
        
        # Add brand attributes
        if context.brand_attributes:
            brand_desc = f"brand style: {', '.join(context.brand_attributes)}"
            components.append(brand_desc)
        
        # Add quality keywords
        components.extend(template.quality_keywords)
        
        # Add technical specifications
        components.extend(template.technical_specs)
        
        # Join components
        prompt = ", ".join(components)
        
        # Optimize for FLUX model
        prompt = self._optimize_for_flux(prompt)
        
        logger.debug(f"Generated prompt: {prompt}")
        return prompt
    
    def _enhance_prompt(self, base_prompt: str, context: ImageContext) -> str:
        """Enhance an existing prompt with context-aware additions."""
        enhancements = []
        
        # Add style keywords based on context
        style_keywords = self._select_style_modifiers(context)
        enhancements.extend(style_keywords)
        
        # Add quality modifiers
        quality_mods = self.quality_modifiers.get(context.image_type, [])
        enhancements.extend(quality_mods[:2])  # Limit to avoid over-specification
        
        # Add technical specs if needed
        if context.technical_specs:
            if context.technical_specs.get('high_quality'):
                enhancements.append("high resolution, professional quality")
        
        # Combine base prompt with enhancements
        if enhancements:
            enhanced = f"{base_prompt}, {', '.join(enhancements)}"
        else:
            enhanced = base_prompt
        
        return self._optimize_for_flux(enhanced)
    
    def _select_style_modifiers(self, context: ImageContext) -> List[str]:
        """Select appropriate style modifiers based on context."""
        modifiers = []
        
        # Get style keywords for image type
        type_styles = self.style_keywords.get(context.image_type, [])
        modifiers.extend(type_styles[:3])  # Limit to top 3
        
        # Get style keywords for website category
        category_styles = self.style_keywords.get(context.website_category, [])
        modifiers.extend(category_styles[:2])  # Limit to top 2
        
        # Add custom style keywords
        if context.style_keywords:
            modifiers.extend(context.style_keywords[:3])
        
        # Remove duplicates while preserving order
        seen = set()
        unique_modifiers = []
        for mod in modifiers:
            if mod not in seen:
                seen.add(mod)
                unique_modifiers.append(mod)
        
        return unique_modifiers
    
    def _optimize_for_flux(self, prompt: str) -> str:
        """Optimize prompt for FLUX model compatibility."""
        # FLUX-specific optimizations
        optimizations = [
            # Ensure proper formatting
            lambda p: p.strip(),
            # Remove excessive punctuation
            lambda p: p.replace(",,", ",").replace("..", "."),
            # Ensure reasonable length (FLUX works well with detailed prompts)
            lambda p: p if len(p) <= 500 else p[:497] + "...",
        ]
        
        optimized = prompt
        for optimization in optimizations:
            optimized = optimization(optimized)
        
        return optimized
    
    def _load_templates(self) -> Dict[Any, PromptTemplate]:
        """Load prompt templates for different contexts."""
        templates = {
            ImageType.HERO: PromptTemplate(
                base_prompt="stunning hero image for website",
                style_modifiers=["cinematic", "dramatic lighting", "wide angle"],
                quality_keywords=["high resolution", "professional photography"],
                technical_specs=["16:9 aspect ratio", "web optimized"]
            ),
            ImageType.PRODUCT: PromptTemplate(
                base_prompt="clean product photography",
                style_modifiers=["minimal background", "studio lighting", "sharp focus"],
                quality_keywords=["commercial photography", "high detail"],
                technical_specs=["square format", "white background"]
            ),
            ImageType.BACKGROUND: PromptTemplate(
                base_prompt="subtle background pattern",
                style_modifiers=["minimal", "abstract", "soft gradients"],
                quality_keywords=["seamless", "non-distracting"],
                technical_specs=["tileable", "low contrast"]
            ),
            ImageType.ICON: PromptTemplate(
                base_prompt="simple icon design",
                style_modifiers=["flat design", "minimal", "geometric"],
                quality_keywords=["vector style", "clean lines"],
                technical_specs=["square format", "transparent background"]
            ),
        }
        
        # Add category-specific templates
        templates[(ImageType.HERO, WebsiteCategory.BUSINESS)] = PromptTemplate(
            base_prompt="professional business hero image",
            style_modifiers=["corporate", "modern", "trustworthy"],
            quality_keywords=["professional photography", "high quality"],
            technical_specs=["16:9 aspect ratio", "web banner"]
        )
        
        return templates
    
    def _load_style_keywords(self) -> Dict[Any, List[str]]:
        """Load style keywords for different contexts."""
        return {
            ImageType.HERO: ["cinematic", "dramatic", "inspiring", "bold"],
            ImageType.PRODUCT: ["clean", "minimal", "professional", "detailed"],
            ImageType.BACKGROUND: ["subtle", "abstract", "textured", "gradient"],
            ImageType.ICON: ["flat", "minimal", "geometric", "symbolic"],
            
            WebsiteCategory.BUSINESS: ["professional", "corporate", "trustworthy", "modern"],
            WebsiteCategory.CREATIVE: ["artistic", "vibrant", "unique", "expressive"],
            WebsiteCategory.TECH: ["futuristic", "digital", "innovative", "sleek"],
            WebsiteCategory.HEALTH: ["clean", "calming", "natural", "caring"],
        }
    
    def _load_quality_modifiers(self) -> Dict[ImageType, List[str]]:
        """Load quality modifiers for different image types."""
        return {
            ImageType.HERO: ["high resolution", "professional photography", "award winning"],
            ImageType.PRODUCT: ["commercial photography", "studio quality", "sharp detail"],
            ImageType.BACKGROUND: ["seamless pattern", "high quality texture"],
            ImageType.ICON: ["vector graphics", "crisp lines", "scalable design"],
        }
    
    def _get_default_template(self) -> PromptTemplate:
        """Get default template for unknown contexts."""
        return PromptTemplate(
            base_prompt="high quality image for web design",
            style_modifiers=["modern", "clean", "professional"],
            quality_keywords=["high resolution", "web optimized"],
            technical_specs=["digital art", "contemporary style"]
        )
