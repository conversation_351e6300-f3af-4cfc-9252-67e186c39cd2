"""
Image processing and optimization for web use.
"""

import os
import hashlib
import logging
import io
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import base64

from PIL import Image, ImageOps
import aiofiles

from ..config import config


logger = logging.getLogger(__name__)


@dataclass
class ImageFormat:
    """Image format configuration."""
    extension: str
    mime_type: str
    quality: int = 85
    optimize: bool = True


@dataclass
class ProcessedImage:
    """Processed image result."""
    file_path: str
    format: str
    size: Tuple[int, int]
    file_size: int
    base64_data: Optional[str] = None


class ImageProcessor:
    """Handles image processing, optimization, and caching."""
    
    def __init__(self):
        """Initialize the image processor."""
        self.cache_dir = Path(config.cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Supported formats
        self.formats = {
            'webp': ImageFormat('webp', 'image/webp', quality=85),
            'png': ImageFormat('png', 'image/png', optimize=True),
            'jpeg': ImageFormat('jpg', 'image/jpeg', quality=90),
            'avif': ImageFormat('avif', 'image/avif', quality=80),
        }
        
        logger.info("Image processor initialized")
    
    async def process_image(
        self,
        image_data: bytes,
        target_format: str = 'webp',
        target_size: Optional[Tuple[int, int]] = None,
        quality: Optional[int] = None,
        include_base64: bool = False
    ) -> ProcessedImage:
        """Process and optimize an image.
        
        Args:
            image_data: Raw image data
            target_format: Target format (webp, png, jpeg, avif)
            target_size: Target size as (width, height), None to keep original
            quality: Quality setting (overrides format default)
            include_base64: Whether to include base64 encoded data
            
        Returns:
            ProcessedImage with file path and metadata
        """
        # Validate format
        if target_format not in self.formats:
            raise ValueError(f"Unsupported format: {target_format}")
        
        format_config = self.formats[target_format]
        
        # Open and process image
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary (for JPEG compatibility)
        if target_format in ['jpeg', 'jpg'] and image.mode in ['RGBA', 'P']:
            # Create white background for transparency
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # Resize if requested
        if target_size:
            image = self._resize_image(image, target_size)
        
        # Optimize image
        image = self._optimize_image(image)
        
        # Generate cache key
        cache_key = self._generate_cache_key(image_data, target_format, target_size, quality)
        
        # Check cache first
        cached_path = await self._get_cached_image(cache_key, target_format)
        if cached_path and os.path.exists(cached_path):
            logger.debug(f"Using cached image: {cached_path}")
            return await self._create_processed_result(
                cached_path, target_format, image.size, include_base64
            )
        
        # Save processed image
        file_path = await self._save_image(
            image, cache_key, format_config, quality or format_config.quality
        )
        
        # Create result
        result = await self._create_processed_result(
            file_path, target_format, image.size, include_base64
        )
        
        logger.info(f"Processed image: {result.file_path} ({result.file_size} bytes)")
        return result
    
    async def create_responsive_set(
        self,
        image_data: bytes,
        sizes: List[Tuple[int, int]],
        formats: List[str] = None
    ) -> Dict[str, List[ProcessedImage]]:
        """Create a responsive image set with multiple sizes and formats.
        
        Args:
            image_data: Raw image data
            sizes: List of (width, height) tuples
            formats: List of formats to generate (defaults to webp, png)
            
        Returns:
            Dictionary mapping format to list of ProcessedImage objects
        """
        if formats is None:
            formats = ['webp', 'png']
        
        results = {}
        
        for format_name in formats:
            format_results = []
            for size in sizes:
                processed = await self.process_image(
                    image_data, 
                    target_format=format_name,
                    target_size=size
                )
                format_results.append(processed)
            results[format_name] = format_results
        
        return results
    
    def _resize_image(self, image: Image.Image, target_size: Tuple[int, int]) -> Image.Image:
        """Resize image while maintaining aspect ratio."""
        # Calculate aspect ratios
        original_ratio = image.width / image.height
        target_ratio = target_size[0] / target_size[1]
        
        if original_ratio > target_ratio:
            # Image is wider, fit to width
            new_width = target_size[0]
            new_height = int(target_size[0] / original_ratio)
        else:
            # Image is taller, fit to height
            new_height = target_size[1]
            new_width = int(target_size[1] * original_ratio)
        
        # Resize with high-quality resampling
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # If exact size is needed, crop or pad
        if (new_width, new_height) != target_size:
            # Center crop to exact size
            resized = ImageOps.fit(resized, target_size, Image.Resampling.LANCZOS)
        
        return resized
    
    def _optimize_image(self, image: Image.Image) -> Image.Image:
        """Apply optimization techniques to the image."""
        # Remove EXIF data for privacy and size reduction
        if hasattr(image, '_getexif'):
            image = image.copy()
        
        return image
    
    def _generate_cache_key(
        self,
        image_data: bytes,
        format_name: str,
        size: Optional[Tuple[int, int]],
        quality: Optional[int]
    ) -> str:
        """Generate a cache key for the image."""
        # Create hash from image data and parameters
        hasher = hashlib.sha256()
        hasher.update(image_data)
        hasher.update(format_name.encode())
        if size:
            hasher.update(f"{size[0]}x{size[1]}".encode())
        if quality:
            hasher.update(str(quality).encode())
        
        return hasher.hexdigest()[:16]
    
    async def _get_cached_image(self, cache_key: str, format_name: str) -> Optional[str]:
        """Check if image exists in cache and is still valid."""
        cache_path = self.cache_dir / f"{cache_key}.{self.formats[format_name].extension}"
        
        if not cache_path.exists():
            return None
        
        # Check if cache is still valid
        if config.cache_ttl_hours > 0:
            file_age = datetime.now() - datetime.fromtimestamp(cache_path.stat().st_mtime)
            if file_age > timedelta(hours=config.cache_ttl_hours):
                # Cache expired, remove file
                try:
                    cache_path.unlink()
                except OSError:
                    pass
                return None
        
        return str(cache_path)
    
    async def _save_image(
        self,
        image: Image.Image,
        cache_key: str,
        format_config: ImageFormat,
        quality: int
    ) -> str:
        """Save image to cache directory."""
        file_path = self.cache_dir / f"{cache_key}.{format_config.extension}"
        
        # Prepare save parameters
        save_kwargs = {}
        if format_config.extension in ['jpeg', 'jpg', 'webp']:
            save_kwargs['quality'] = quality
        if format_config.optimize:
            save_kwargs['optimize'] = True
        
        # Save image
        image.save(file_path, format=format_config.extension.upper(), **save_kwargs)
        
        return str(file_path)
    
    async def _create_processed_result(
        self,
        file_path: str,
        format_name: str,
        size: Tuple[int, int],
        include_base64: bool
    ) -> ProcessedImage:
        """Create ProcessedImage result object."""
        file_size = os.path.getsize(file_path)
        
        base64_data = None
        if include_base64:
            async with aiofiles.open(file_path, 'rb') as f:
                image_bytes = await f.read()
                base64_data = base64.b64encode(image_bytes).decode('utf-8')
        
        return ProcessedImage(
            file_path=file_path,
            format=format_name,
            size=size,
            file_size=file_size,
            base64_data=base64_data
        )
    
    async def cleanup_cache(self):
        """Clean up expired cache files."""
        if not config.cache_enabled or config.cache_ttl_hours <= 0:
            return
        
        cutoff_time = datetime.now() - timedelta(hours=config.cache_ttl_hours)
        cleaned_count = 0
        
        for file_path in self.cache_dir.iterdir():
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except OSError as e:
                        logger.warning(f"Failed to delete cache file {file_path}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} expired cache files")
    
    async def get_cache_stats(self) -> Dict[str, Union[int, float]]:
        """Get cache statistics."""
        if not self.cache_dir.exists():
            return {'file_count': 0, 'total_size_mb': 0.0}
        
        file_count = 0
        total_size = 0
        
        for file_path in self.cache_dir.iterdir():
            if file_path.is_file():
                file_count += 1
                total_size += file_path.stat().st_size
        
        return {
            'file_count': file_count,
            'total_size_mb': total_size / (1024 * 1024)
        }
