# Web Design Image Generation MCP Server

A Model Context Protocol (MCP) server that generates contextually appropriate images for web design using ModelScope's FLUX model API. This server analyzes webpage content and automatically creates optimized images that fit the design context.

## 🚀 Features

- **Context-Aware Generation**: Analyzes HTML/CSS to understand image requirements
- **Intelligent Prompts**: Automatically generates optimized prompts based on webpage context
- **Multiple Image Types**: Supports hero images, products, backgrounds, icons, and more
- **Web Optimization**: Outputs images in web-friendly formats (WebP, AVIF, PNG, JPEG)
- **Responsive Images**: Creates multiple sizes for responsive design
- **Caching System**: Efficient caching to reduce API calls and improve performance
- **MCP Compliant**: Full integration with Claude Desktop and other MCP clients

## 📋 Requirements

- Python 3.9+
- ModelScope API key (provided by MCP client)
- MCP-compatible client (Claude Desktop, etc.)

## 🛠️ Installation

### Using uv (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd web-design-image-mcp

# Install with uv
uv add "."

# Or install in development mode
uv add -e ".[dev]"
```

### Using pip

```bash
# Clone the repository
git clone <repository-url>
cd web-design-image-mcp

# Install dependencies
pip install -r requirements.txt

# Or install the package
pip install -e .
```

## ⚙️ Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Configure your settings in `.env`:
```env
# ModelScope API Configuration
MODELSCOPE_API_KEY=your_api_key_here

# Server Configuration
LOG_LEVEL=INFO
CACHE_ENABLED=true
CACHE_DIR=./cache/images

# Image Settings
DEFAULT_IMAGE_WIDTH=1024
DEFAULT_IMAGE_HEIGHT=1024
SUPPORTED_FORMATS=webp,png,jpeg,avif
```

**Note**: In production, the API key should be provided by the MCP client, not hardcoded.

## 🔧 Usage

### Running the Server

#### Development Mode
```bash
# Using uv
uv run mcp dev src/web_design_image_mcp/server.py

# Using Python directly
python -m web_design_image_mcp.server
```

#### Claude Desktop Integration
```bash
# Install for Claude Desktop
uv run mcp install src/web_design_image_mcp/server.py --name "Web Design Image Generator"
```

### MCP Tools

The server provides three main tools:

#### 1. `generate_web_image`
Generate images for web design use.

```python
# Example usage in Claude
generate_web_image(
    prompt="modern office building with glass facade",
    image_type="hero",
    website_category="business",
    width=1920,
    height=1080,
    format="webp",
    api_key="your_api_key"
)
```

#### 2. `analyze_web_context`
Analyze webpage HTML/CSS to understand image requirements.

```python
# Example usage
analyze_web_context(
    html_content="<html>...</html>",
    css_content="body { ... }"
)
```

#### 3. `suggest_image_prompts`
Get optimized prompt suggestions for different contexts.

```python
# Example usage
suggest_image_prompts(
    content_description="team collaboration photo",
    image_type="hero",
    website_category="business",
    style_keywords=["modern", "professional"],
    color_scheme=["#2563eb", "#1e40af"]
)
```

### MCP Resources

Access helpful resources:

- `templates://image-types` - Available image types and their use cases
- `templates://website-categories` - Website categories and style keywords
- `examples://prompts/{type}` - Example prompts for each image type
- `config://server-info` - Server configuration and capabilities

## 🎯 Image Types

| Type | Description | Typical Size | Use Cases |
|------|-------------|--------------|-----------|
| `hero` | Large banner images | 1920x1080 | Landing pages, headers |
| `product` | Product photography | 800x800 | E-commerce, catalogs |
| `background` | Subtle patterns/textures | 1920x1080 | Page backgrounds |
| `icon` | Simple symbolic graphics | 64x64 | Navigation, features |
| `avatar` | Profile pictures | 200x200 | User profiles, teams |
| `logo` | Brand logos | 256x256 | Branding, headers |
| `thumbnail` | Small preview images | 300x200 | Blog posts, galleries |

## 🌐 Website Categories

| Category | Description | Style Keywords |
|----------|-------------|----------------|
| `business` | Corporate websites | professional, trustworthy, clean |
| `ecommerce` | Online stores | product-focused, commercial, appealing |
| `creative` | Artist portfolios | artistic, unique, expressive, bold |
| `tech` | Technology companies | modern, innovative, digital, sleek |
| `health` | Healthcare websites | clean, caring, natural, calming |
| `education` | Educational sites | clear, informative, accessible |

## 🔒 Security

- API keys are provided by MCP clients, never hardcoded
- Input validation for all parameters
- Secure file handling and caching
- Rate limiting to prevent abuse

## 📊 Performance

- Intelligent caching system reduces API calls
- Image optimization for web delivery
- Concurrent request limiting
- Automatic cache cleanup

## 🧪 Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=src/web_design_image_mcp

# Run specific test types
pytest -m unit
pytest -m integration
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🔗 Related Projects

- [Model Context Protocol](https://modelcontextprotocol.io/)
- [Claude Desktop](https://claude.ai/download)
- [ModelScope](https://modelscope.cn/)

---

Built with ❤️ for the MCP community
