"""
Configuration management for the web design image MCP server.
"""

import os
from typing import List, Optional
from pydantic import BaseModel, <PERSON>
from pydantic_settings import BaseSettings


class ServerConfig(BaseSettings):
    """Configuration settings for the MCP server."""
    
    # Server Information
    server_name: str = Field(default="Web Design Image Generator", env="SERVER_NAME")
    server_version: str = Field(default="0.1.0", env="SERVER_VERSION")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # ModelScope API Configuration
    # Note: API key should be provided by MCP client in production
    modelscope_api_key: Optional[str] = Field(default=None, env="MODELSCOPE_API_KEY")
    modelscope_api_url: str = Field(
        default="https://api-inference.modelscope.cn/v1/images/generations",
        env="MODELSCOPE_API_URL"
    )
    modelscope_model: str = Field(
        default="MusePublic/489_ckpt_FLUX_1",
        env="MODELSCOPE_MODEL"
    )
    
    # Image Generation Settings
    default_image_width: int = Field(default=1024, env="DEFAULT_IMAGE_WIDTH")
    default_image_height: int = Field(default=1024, env="DEFAULT_IMAGE_HEIGHT")
    max_image_size: int = Field(default=2048, env="MAX_IMAGE_SIZE")
    supported_formats: List[str] = Field(
        default=["webp", "png", "jpeg", "avif"],
        env="SUPPORTED_FORMATS"
    )
    
    # Cache Settings
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_dir: str = Field(default="./cache/images", env="CACHE_DIR")
    cache_max_size_mb: int = Field(default=1000, env="CACHE_MAX_SIZE_MB")
    cache_ttl_hours: int = Field(default=24, env="CACHE_TTL_HOURS")
    
    # Rate Limiting
    max_requests_per_minute: int = Field(default=10, env="MAX_REQUESTS_PER_MINUTE")
    max_concurrent_requests: int = Field(default=3, env="MAX_CONCURRENT_REQUESTS")
    
    # Retry Configuration
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    retry_delay_seconds: float = Field(default=1.0, env="RETRY_DELAY_SECONDS")
    backoff_multiplier: float = Field(default=2.0, env="BACKOFF_MULTIPLIER")
    
    # Development Settings
    debug: bool = Field(default=False, env="DEBUG")
    development_mode: bool = Field(default=False, env="DEVELOPMENT_MODE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure cache directory exists
        if self.cache_enabled:
            os.makedirs(self.cache_dir, exist_ok=True)


# Global configuration instance
config = ServerConfig()
