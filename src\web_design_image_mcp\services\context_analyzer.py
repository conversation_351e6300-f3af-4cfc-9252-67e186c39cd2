"""
Web context analysis for intelligent image generation.
"""

import re
import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from urllib.parse import urlparse

from bs4 import BeautifulSoup, Tag
from .prompt_generator import ImageType, WebsiteCategory, ImageContext


logger = logging.getLogger(__name__)


@dataclass
class WebPageContext:
    """Context extracted from a web page."""
    title: str
    description: str
    content_type: WebsiteCategory
    color_scheme: List[str]
    style_attributes: List[str]
    image_placeholders: List['ImagePlaceholder']
    semantic_sections: Dict[str, str]


@dataclass
class ImagePlaceholder:
    """Information about an image placeholder in the page."""
    element_type: str  # img, div, section, etc.
    alt_text: Optional[str]
    class_names: List[str]
    context_text: str
    suggested_type: ImageType
    dimensions: Optional[Tuple[int, int]]


class WebContextAnalyzer:
    """Analyzes web page context to determine appropriate image requirements."""
    
    def __init__(self):
        """Initialize the web context analyzer."""
        self.image_type_patterns = self._load_image_type_patterns()
        self.website_category_keywords = self._load_category_keywords()
        self.color_extractors = self._load_color_extractors()
        logger.info("Web context analyzer initialized")
    
    def analyze_html(self, html_content: str, css_content: str = "") -> WebPageContext:
        """Analyze HTML content to extract context for image generation.
        
        Args:
            html_content: HTML content of the page
            css_content: CSS content (optional)
            
        Returns:
            WebPageContext with extracted information
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extract basic page information
        title = self._extract_title(soup)
        description = self._extract_description(soup)
        
        # Analyze content type
        content_type = self._classify_website_category(soup, title, description)
        
        # Extract color scheme
        color_scheme = self._extract_color_scheme(soup, css_content)
        
        # Extract style attributes
        style_attributes = self._extract_style_attributes(soup, css_content)
        
        # Find image placeholders
        image_placeholders = self._find_image_placeholders(soup)
        
        # Extract semantic sections
        semantic_sections = self._extract_semantic_sections(soup)
        
        context = WebPageContext(
            title=title,
            description=description,
            content_type=content_type,
            color_scheme=color_scheme,
            style_attributes=style_attributes,
            image_placeholders=image_placeholders,
            semantic_sections=semantic_sections
        )
        
        logger.info(f"Analyzed web context: {content_type.value} site with {len(image_placeholders)} placeholders")
        return context
    
    def suggest_image_context(
        self, 
        placeholder: ImagePlaceholder, 
        page_context: WebPageContext
    ) -> ImageContext:
        """Suggest image context for a specific placeholder.
        
        Args:
            placeholder: Image placeholder information
            page_context: Overall page context
            
        Returns:
            ImageContext for generating appropriate image
        """
        # Determine content description
        content_description = self._generate_content_description(placeholder, page_context)
        
        # Select style keywords
        style_keywords = self._select_style_keywords(placeholder, page_context)
        
        # Determine brand attributes
        brand_attributes = self._extract_brand_attributes(page_context)
        
        # Set technical specifications
        technical_specs = {
            'high_quality': True,
            'web_optimized': True,
            'responsive': True
        }
        
        if placeholder.dimensions:
            technical_specs['width'] = placeholder.dimensions[0]
            technical_specs['height'] = placeholder.dimensions[1]
        
        return ImageContext(
            image_type=placeholder.suggested_type,
            website_category=page_context.content_type,
            content_description=content_description,
            style_keywords=style_keywords,
            color_scheme=page_context.color_scheme,
            brand_attributes=brand_attributes,
            technical_specs=technical_specs
        )
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract page title."""
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.get_text().strip()
        
        # Fallback to h1
        h1_tag = soup.find('h1')
        if h1_tag:
            return h1_tag.get_text().strip()
        
        return "Untitled Page"
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract page description."""
        # Try meta description first
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            return meta_desc['content'].strip()
        
        # Try Open Graph description
        og_desc = soup.find('meta', attrs={'property': 'og:description'})
        if og_desc and og_desc.get('content'):
            return og_desc['content'].strip()
        
        # Fallback to first paragraph
        first_p = soup.find('p')
        if first_p:
            text = first_p.get_text().strip()
            return text[:200] + "..." if len(text) > 200 else text
        
        return ""
    
    def _classify_website_category(
        self, 
        soup: BeautifulSoup, 
        title: str, 
        description: str
    ) -> WebsiteCategory:
        """Classify the website category based on content."""
        content_text = f"{title} {description}".lower()
        
        # Add body text for better classification
        body_text = ""
        if soup.body:
            body_text = soup.body.get_text()[:1000].lower()  # First 1000 chars
        
        full_text = f"{content_text} {body_text}"
        
        # Score each category
        category_scores = {}
        for category, keywords in self.website_category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in full_text)
            category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            if category_scores[best_category] > 0:
                return best_category
        
        # Default fallback
        return WebsiteCategory.BUSINESS
    
    def _extract_color_scheme(self, soup: BeautifulSoup, css_content: str) -> List[str]:
        """Extract color scheme from HTML and CSS."""
        colors = set()
        
        # Extract from inline styles
        for element in soup.find_all(style=True):
            style = element['style']
            found_colors = re.findall(r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}', style)
            colors.update(found_colors)
        
        # Extract from CSS content
        if css_content:
            found_colors = re.findall(r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}', css_content)
            colors.update(found_colors)
        
        # Convert to color names or keep hex
        color_list = list(colors)[:5]  # Limit to 5 colors
        return color_list
    
    def _extract_style_attributes(self, soup: BeautifulSoup, css_content: str) -> List[str]:
        """Extract style attributes from the page."""
        attributes = []
        
        # Check for common design frameworks
        if soup.find(class_=re.compile(r'bootstrap|bs-')):
            attributes.append("bootstrap")
        if soup.find(class_=re.compile(r'material|md-')):
            attributes.append("material design")
        if soup.find(class_=re.compile(r'tailwind|tw-')):
            attributes.append("tailwind")
        
        # Check for design patterns
        if soup.find(class_=re.compile(r'hero|banner')):
            attributes.append("hero section")
        if soup.find(class_=re.compile(r'card|panel')):
            attributes.append("card layout")
        if soup.find(class_=re.compile(r'grid|flex')):
            attributes.append("grid layout")
        
        return attributes
    
    def _find_image_placeholders(self, soup: BeautifulSoup) -> List[ImagePlaceholder]:
        """Find image placeholders in the HTML."""
        placeholders = []
        
        # Find img tags with placeholder attributes
        for img in soup.find_all('img'):
            placeholder = self._analyze_img_element(img)
            if placeholder:
                placeholders.append(placeholder)
        
        # Find divs that might be image placeholders
        for div in soup.find_all('div'):
            placeholder = self._analyze_div_element(div)
            if placeholder:
                placeholders.append(placeholder)
        
        return placeholders
    
    def _analyze_img_element(self, img: Tag) -> Optional[ImagePlaceholder]:
        """Analyze an img element to determine if it's a placeholder."""
        src = img.get('src', '')
        alt = img.get('alt', '')
        classes = img.get('class', [])
        
        # Check if it's a placeholder
        placeholder_indicators = [
            'placeholder', 'dummy', 'example', 'sample',
            'via.placeholder', 'picsum', 'lorempixel'
        ]
        
        is_placeholder = any(
            indicator in src.lower() or 
            indicator in alt.lower() or
            any(indicator in cls.lower() for cls in classes)
            for indicator in placeholder_indicators
        )
        
        if not is_placeholder and src and not src.startswith('data:'):
            # Has real source, probably not a placeholder
            return None
        
        # Determine image type
        suggested_type = self._determine_image_type(img, classes, alt)
        
        # Get context text
        context_text = self._get_context_text(img)
        
        # Extract dimensions
        dimensions = self._extract_dimensions(img)
        
        return ImagePlaceholder(
            element_type='img',
            alt_text=alt,
            class_names=classes,
            context_text=context_text,
            suggested_type=suggested_type,
            dimensions=dimensions
        )
    
    def _analyze_div_element(self, div: Tag) -> Optional[ImagePlaceholder]:
        """Analyze a div element to see if it's an image placeholder."""
        classes = div.get('class', [])
        
        # Check for image-related classes
        image_indicators = [
            'image', 'img', 'photo', 'picture', 'banner', 'hero',
            'thumbnail', 'avatar', 'logo', 'icon'
        ]
        
        has_image_class = any(
            any(indicator in cls.lower() for indicator in image_indicators)
            for cls in classes
        )
        
        if not has_image_class:
            return None
        
        # Determine image type
        suggested_type = self._determine_image_type(div, classes)
        
        # Get context text
        context_text = self._get_context_text(div)
        
        # Extract dimensions
        dimensions = self._extract_dimensions(div)
        
        return ImagePlaceholder(
            element_type='div',
            alt_text=None,
            class_names=classes,
            context_text=context_text,
            suggested_type=suggested_type,
            dimensions=dimensions
        )
    
    def _determine_image_type(
        self, 
        element: Tag, 
        classes: List[str], 
        alt_text: str = ""
    ) -> ImageType:
        """Determine the type of image based on element context."""
        text_to_analyze = f"{' '.join(classes)} {alt_text}".lower()
        
        # Check patterns for each image type
        for image_type, patterns in self.image_type_patterns.items():
            if any(pattern in text_to_analyze for pattern in patterns):
                return image_type
        
        # Default to photo
        return ImageType.PHOTO
    
    def _get_context_text(self, element: Tag) -> str:
        """Get contextual text around an element."""
        context_parts = []
        
        # Get text from the element itself
        element_text = element.get_text().strip()
        if element_text:
            context_parts.append(element_text)
        
        # Get text from parent elements
        parent = element.parent
        if parent and parent.name not in ['html', 'body']:
            parent_text = parent.get_text().strip()
            if parent_text and len(parent_text) < 200:
                context_parts.append(parent_text)
        
        # Get text from siblings
        for sibling in element.find_next_siblings(limit=2):
            if sibling.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p']:
                sibling_text = sibling.get_text().strip()
                if sibling_text:
                    context_parts.append(sibling_text)
                    break
        
        return " ".join(context_parts)[:300]  # Limit length
    
    def _extract_dimensions(self, element: Tag) -> Optional[Tuple[int, int]]:
        """Extract dimensions from element attributes or styles."""
        width = element.get('width')
        height = element.get('height')
        
        if width and height:
            try:
                return (int(width), int(height))
            except ValueError:
                pass
        
        # Check style attribute
        style = element.get('style', '')
        width_match = re.search(r'width:\s*(\d+)px', style)
        height_match = re.search(r'height:\s*(\d+)px', style)
        
        if width_match and height_match:
            try:
                return (int(width_match.group(1)), int(height_match.group(1)))
            except ValueError:
                pass
        
        return None
    
    def _extract_semantic_sections(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract semantic sections from the page."""
        sections = {}
        
        # Common semantic elements
        semantic_tags = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer']
        
        for tag_name in semantic_tags:
            elements = soup.find_all(tag_name)
            if elements:
                sections[tag_name] = elements[0].get_text()[:200]  # First 200 chars
        
        return sections
    
    def _generate_content_description(
        self, 
        placeholder: ImagePlaceholder, 
        page_context: WebPageContext
    ) -> str:
        """Generate content description for the image."""
        parts = []
        
        # Use alt text if available
        if placeholder.alt_text:
            parts.append(placeholder.alt_text)
        
        # Use context text
        if placeholder.context_text:
            parts.append(placeholder.context_text[:100])
        
        # Add page context
        if page_context.description:
            parts.append(f"for {page_context.content_type.value} website")
        
        return " ".join(parts) if parts else f"{placeholder.suggested_type.value} image"
    
    def _select_style_keywords(
        self, 
        placeholder: ImagePlaceholder, 
        page_context: WebPageContext
    ) -> List[str]:
        """Select style keywords based on context."""
        keywords = []
        
        # Add style attributes from page
        keywords.extend(page_context.style_attributes[:3])
        
        # Add keywords based on classes
        for class_name in placeholder.class_names:
            if 'modern' in class_name.lower():
                keywords.append('modern')
            elif 'vintage' in class_name.lower():
                keywords.append('vintage')
            elif 'minimal' in class_name.lower():
                keywords.append('minimal')
        
        return keywords[:5]  # Limit to 5 keywords
    
    def _extract_brand_attributes(self, page_context: WebPageContext) -> List[str]:
        """Extract brand attributes from page context."""
        attributes = []
        
        # Based on website category
        category_attributes = {
            WebsiteCategory.BUSINESS: ['professional', 'trustworthy'],
            WebsiteCategory.CREATIVE: ['artistic', 'innovative'],
            WebsiteCategory.TECH: ['modern', 'cutting-edge'],
            WebsiteCategory.HEALTH: ['clean', 'caring'],
        }
        
        attributes.extend(category_attributes.get(page_context.content_type, []))
        
        return attributes
    
    def _load_image_type_patterns(self) -> Dict[ImageType, List[str]]:
        """Load patterns for identifying image types."""
        return {
            ImageType.HERO: ['hero', 'banner', 'jumbotron', 'masthead'],
            ImageType.PRODUCT: ['product', 'item', 'catalog', 'shop'],
            ImageType.BACKGROUND: ['background', 'bg', 'backdrop'],
            ImageType.ICON: ['icon', 'symbol', 'glyph', 'pictogram'],
            ImageType.AVATAR: ['avatar', 'profile', 'user', 'person'],
            ImageType.LOGO: ['logo', 'brand', 'company'],
            ImageType.THUMBNAIL: ['thumb', 'preview', 'small'],
        }
    
    def _load_category_keywords(self) -> Dict[WebsiteCategory, List[str]]:
        """Load keywords for website category classification."""
        return {
            WebsiteCategory.BUSINESS: ['business', 'company', 'corporate', 'services', 'professional'],
            WebsiteCategory.ECOMMERCE: ['shop', 'store', 'buy', 'product', 'cart', 'checkout'],
            WebsiteCategory.BLOG: ['blog', 'article', 'post', 'news', 'journal'],
            WebsiteCategory.PORTFOLIO: ['portfolio', 'work', 'projects', 'gallery', 'showcase'],
            WebsiteCategory.TECH: ['technology', 'software', 'app', 'digital', 'innovation'],
            WebsiteCategory.HEALTH: ['health', 'medical', 'wellness', 'care', 'treatment'],
        }
    
    def _load_color_extractors(self) -> Dict[str, str]:
        """Load color extraction patterns."""
        return {
            'hex': r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}',
            'rgb': r'rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)',
            'rgba': r'rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)',
        }
