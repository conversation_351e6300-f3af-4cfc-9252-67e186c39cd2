"""
Basic usage example for the Web Design Image MCP Server.
"""

import asyncio
from web_design_image_mcp.services.prompt_generator import PromptGenerator, ImageContext, ImageType, WebsiteCategory
from web_design_image_mcp.services.context_analyzer import WebContextAnalyzer


async def example_prompt_generation():
    """Example of generating prompts for different contexts."""
    print("=== Prompt Generation Example ===")
    
    generator = PromptGenerator()
    
    # Create different image contexts
    contexts = [
        ImageContext(
            image_type=ImageType.HERO,
            website_category=WebsiteCategory.BUSINESS,
            content_description="professional team collaboration",
            style_keywords=["modern", "corporate"],
            color_scheme=["#2563eb", "#1e40af"]
        ),
        ImageContext(
            image_type=ImageType.PRODUCT,
            website_category=WebsiteCategory.ECOMMERCE,
            content_description="elegant smartphone",
            style_keywords=["minimal", "premium"],
            color_scheme=["#000000", "#ffffff"]
        ),
        ImageContext(
            image_type=ImageType.BACKGROUND,
            website_category=WebsiteCategory.CREATIVE,
            content_description="abstract artistic pattern",
            style_keywords=["artistic", "vibrant"],
            color_scheme=["#ff6b6b", "#4ecdc4"]
        )
    ]
    
    for i, context in enumerate(contexts, 1):
        prompt = generator.generate_prompt(context)
        print(f"\n{i}. {context.image_type.value.title()} for {context.website_category.value} site:")
        print(f"   Prompt: {prompt}")


async def example_web_analysis():
    """Example of analyzing web page context."""
    print("\n=== Web Context Analysis Example ===")
    
    analyzer = WebContextAnalyzer()
    
    # Sample HTML content
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>TechCorp - Innovative Solutions</title>
        <meta name="description" content="Leading technology company providing innovative software solutions">
    </head>
    <body>
        <header class="hero-section">
            <h1>Welcome to TechCorp</h1>
            <img src="placeholder.jpg" alt="team collaboration" class="hero-image">
        </header>
        <section class="products">
            <h2>Our Products</h2>
            <div class="product-grid">
                <div class="product-card">
                    <img src="product1.jpg" alt="innovative software dashboard" class="product-image">
                    <h3>Dashboard Pro</h3>
                </div>
                <div class="product-card">
                    <img src="product2.jpg" alt="mobile app interface" class="product-image">
                    <h3>Mobile Suite</h3>
                </div>
            </div>
        </section>
    </body>
    </html>
    """
    
    css_content = """
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .product-card {
        border: 1px solid #e2e8f0;
        border-radius: 8px;
    }
    """
    
    # Analyze the page
    context = analyzer.analyze_html(html_content, css_content)
    
    print(f"Page Title: {context.title}")
    print(f"Content Type: {context.content_type.value}")
    print(f"Style Attributes: {context.style_attributes}")
    print(f"Color Scheme: {context.color_scheme}")
    print(f"Found {len(context.image_placeholders)} image placeholders:")
    
    for i, placeholder in enumerate(context.image_placeholders, 1):
        print(f"  {i}. {placeholder.element_type} - {placeholder.suggested_type.value}")
        print(f"     Alt text: {placeholder.alt_text}")
        print(f"     Context: {placeholder.context_text[:100]}...")
        
        # Generate image context for this placeholder
        image_context = analyzer.suggest_image_context(placeholder, context)
        prompt = PromptGenerator().generate_prompt(image_context)
        print(f"     Suggested prompt: {prompt}")
        print()


async def main():
    """Run all examples."""
    await example_prompt_generation()
    await example_web_analysis()


if __name__ == "__main__":
    asyncio.run(main())
