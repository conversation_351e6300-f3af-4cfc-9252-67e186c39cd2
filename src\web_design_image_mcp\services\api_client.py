"""
ModelScope API client for image generation.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

import httpx
from PIL import Image
from io import BytesIO

from ..config import config


logger = logging.getLogger(__name__)


@dataclass
class ImageGenerationRequest:
    """Request parameters for image generation."""
    prompt: str
    width: int = 1024
    height: int = 1024
    model: str = "MusePublic/489_ckpt_FLUX_1"


@dataclass
class ImageGenerationResult:
    """Result of image generation."""
    image_url: str
    image_data: bytes
    metadata: Dict[str, Any]
    generation_time: datetime
    request_params: ImageGenerationRequest


class ModelScopeAPIError(Exception):
    """Exception raised for ModelScope API errors."""
    pass


class ModelScopeClient:
    """Client for interacting with ModelScope image generation API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the ModelScope client.
        
        Args:
            api_key: API key for authentication. If None, will use config or expect
                    it to be provided by MCP client.
        """
        self.api_key = api_key or config.modelscope_api_key
        self.api_url = config.modelscope_api_url
        self.default_model = config.modelscope_model
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay_seconds
        self.backoff_multiplier = config.backoff_multiplier
        
        # Create HTTP client with timeout and limits
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0),
            limits=httpx.Limits(max_connections=config.max_concurrent_requests)
        )
        
        logger.info("ModelScope client initialized")
    
    async def generate_image(
        self, 
        prompt: str, 
        width: int = None, 
        height: int = None,
        api_key: Optional[str] = None
    ) -> ImageGenerationResult:
        """Generate an image using the ModelScope API.
        
        Args:
            prompt: Text prompt for image generation
            width: Image width (defaults to config value)
            height: Image height (defaults to config value)
            api_key: API key to use (overrides instance key)
            
        Returns:
            ImageGenerationResult with the generated image and metadata
            
        Raises:
            ModelScopeAPIError: If the API request fails
        """
        # Use provided API key or fall back to instance key
        auth_key = api_key or self.api_key
        if not auth_key:
            raise ModelScopeAPIError("No API key provided")
        
        # Prepare request
        request = ImageGenerationRequest(
            prompt=prompt,
            width=width or config.default_image_width,
            height=height or config.default_image_height,
            model=self.default_model
        )
        
        logger.info(f"Generating image with prompt: {prompt[:100]}...")
        
        # Make API request with retries
        for attempt in range(self.max_retries + 1):
            try:
                result = await self._make_api_request(request, auth_key)
                logger.info("Image generation successful")
                return result
                
            except Exception as e:
                if attempt == self.max_retries:
                    logger.error(f"Image generation failed after {self.max_retries} retries: {e}")
                    raise ModelScopeAPIError(f"Failed to generate image: {e}")
                
                delay = self.retry_delay * (self.backoff_multiplier ** attempt)
                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                await asyncio.sleep(delay)
    
    async def _make_api_request(
        self, 
        request: ImageGenerationRequest, 
        api_key: str
    ) -> ImageGenerationResult:
        """Make the actual API request to ModelScope."""
        payload = {
            'model': request.model,
            'prompt': request.prompt
        }
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # Make the API request
        response = await self.client.post(
            self.api_url,
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            error_msg = f"API request failed with status {response.status_code}: {response.text}"
            raise ModelScopeAPIError(error_msg)
        
        # Parse response
        try:
            response_data = response.json()
            image_url = response_data['images'][0]['url']
        except (KeyError, IndexError, json.JSONDecodeError) as e:
            raise ModelScopeAPIError(f"Invalid API response format: {e}")
        
        # Download the image
        image_data = await self._download_image(image_url)
        
        # Create result
        result = ImageGenerationResult(
            image_url=image_url,
            image_data=image_data,
            metadata={
                'model': request.model,
                'prompt': request.prompt,
                'width': request.width,
                'height': request.height,
                'api_response': response_data
            },
            generation_time=datetime.now(),
            request_params=request
        )
        
        return result
    
    async def _download_image(self, image_url: str) -> bytes:
        """Download image from the provided URL."""
        try:
            response = await self.client.get(image_url)
            response.raise_for_status()
            return response.content
        except Exception as e:
            raise ModelScopeAPIError(f"Failed to download image: {e}")
    
    def validate_image(self, image_data: bytes) -> bool:
        """Validate that the downloaded data is a valid image."""
        try:
            image = Image.open(BytesIO(image_data))
            image.verify()
            return True
        except Exception as e:
            logger.warning(f"Image validation failed: {e}")
            return False
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
        logger.info("ModelScope client closed")
