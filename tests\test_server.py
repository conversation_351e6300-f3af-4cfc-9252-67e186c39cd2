"""
Basic tests for the MCP server.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from web_design_image_mcp.server import create_server
from web_design_image_mcp.services.prompt_generator import ImageType, WebsiteCategory


def test_create_server():
    """Test that the server can be created successfully."""
    server = create_server()
    assert server is not None
    assert server.name == "Web Design Image Generator"


def test_image_type_enum():
    """Test ImageType enum values."""
    assert ImageType.HERO.value == "hero"
    assert ImageType.PRODUCT.value == "product"
    assert ImageType.BACKGROUND.value == "background"
    assert ImageType.ICON.value == "icon"


def test_website_category_enum():
    """Test WebsiteCategory enum values."""
    assert WebsiteCategory.BUSINESS.value == "business"
    assert WebsiteCategory.ECOMMERCE.value == "ecommerce"
    assert WebsiteCategory.CREATIVE.value == "creative"
    assert WebsiteCategory.TECH.value == "tech"


@pytest.mark.asyncio
async def test_server_tools_exist():
    """Test that the server has the expected tools."""
    server = create_server()
    
    # The tools should be registered with the server
    # This is a basic test to ensure the server structure is correct
    assert hasattr(server, '_tools') or hasattr(server, 'tools')


if __name__ == "__main__":
    pytest.main([__file__])
