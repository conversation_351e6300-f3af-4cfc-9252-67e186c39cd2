#!/usr/bin/env python3
"""
Validation script to test the MCP server setup and basic functionality.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from web_design_image_mcp.config import config
from web_design_image_mcp.server import create_server
from web_design_image_mcp.services import PromptGenerator, WebContextAnalyzer, ImageProcessor


def test_imports():
    """Test that all modules can be imported successfully."""
    print("✓ Testing imports...")
    try:
        from web_design_image_mcp.services.api_client import ModelScopeClient
        from web_design_image_mcp.services.prompt_generator import ImageType, WebsiteCategory
        from web_design_image_mcp.services.context_analyzer import WebPageContext
        from web_design_image_mcp.services.image_processor import ProcessedImage
        print("  ✓ All imports successful")
        return True
    except ImportError as e:
        print(f"  ✗ Import error: {e}")
        return False


def test_config():
    """Test configuration loading."""
    print("✓ Testing configuration...")
    try:
        assert config.server_name == "Web Design Image Generator"
        assert config.default_image_width == 1024
        assert config.default_image_height == 1024
        assert isinstance(config.supported_formats, list)
        print("  ✓ Configuration loaded successfully")
        return True
    except Exception as e:
        print(f"  ✗ Configuration error: {e}")
        return False


def test_server_creation():
    """Test MCP server creation."""
    print("✓ Testing server creation...")
    try:
        server = create_server()
        assert server is not None
        assert server.name == config.server_name
        print("  ✓ Server created successfully")
        return True
    except Exception as e:
        print(f"  ✗ Server creation error: {e}")
        return False


def test_services():
    """Test service initialization."""
    print("✓ Testing services...")
    try:
        # Test PromptGenerator
        prompt_gen = PromptGenerator()
        assert prompt_gen is not None
        
        # Test WebContextAnalyzer
        analyzer = WebContextAnalyzer()
        assert analyzer is not None
        
        # Test ImageProcessor
        processor = ImageProcessor()
        assert processor is not None
        
        print("  ✓ All services initialized successfully")
        return True
    except Exception as e:
        print(f"  ✗ Service initialization error: {e}")
        return False


async def test_prompt_generation():
    """Test prompt generation functionality."""
    print("✓ Testing prompt generation...")
    try:
        from web_design_image_mcp.services.prompt_generator import ImageContext, ImageType, WebsiteCategory
        
        generator = PromptGenerator()
        context = ImageContext(
            image_type=ImageType.HERO,
            website_category=WebsiteCategory.BUSINESS,
            content_description="professional team meeting",
            style_keywords=["modern", "corporate"]
        )
        
        prompt = generator.generate_prompt(context)
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        print(f"  ✓ Generated prompt: {prompt[:100]}...")
        return True
    except Exception as e:
        print(f"  ✗ Prompt generation error: {e}")
        return False


async def test_web_analysis():
    """Test web context analysis."""
    print("✓ Testing web context analysis...")
    try:
        analyzer = WebContextAnalyzer()
        
        html = """
        <html>
        <head><title>Test Business Site</title></head>
        <body>
            <img src="placeholder.jpg" alt="team photo" class="hero-image">
            <div class="product-image">Product showcase</div>
        </body>
        </html>
        """
        
        context = analyzer.analyze_html(html)
        assert context.title == "Test Business Site"
        assert len(context.image_placeholders) > 0
        print(f"  ✓ Analyzed page: found {len(context.image_placeholders)} placeholders")
        return True
    except Exception as e:
        print(f"  ✗ Web analysis error: {e}")
        return False


def test_cache_directory():
    """Test cache directory creation."""
    print("✓ Testing cache directory...")
    try:
        cache_path = Path(config.cache_dir)
        assert cache_path.exists()
        assert cache_path.is_dir()
        print(f"  ✓ Cache directory exists: {cache_path}")
        return True
    except Exception as e:
        print(f"  ✗ Cache directory error: {e}")
        return False


async def main():
    """Run all validation tests."""
    print("🔍 Validating Web Design Image MCP Server Setup\n")
    
    tests = [
        test_imports,
        test_config,
        test_server_creation,
        test_services,
        test_prompt_generation,
        test_web_analysis,
        test_cache_directory,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"  ✗ Test failed with exception: {e}")
        
        print()  # Add spacing between tests
    
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The MCP server is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
    
    # Run validation
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
