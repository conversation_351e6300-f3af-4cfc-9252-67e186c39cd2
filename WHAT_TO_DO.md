## Web Design Image Generation MCP Server

Please refer to the documents `@mcprule.txt` and `@mcprule-ts.txt` to help develop an **MCP server for generating images used in web design**.

### Purpose

When a user is designing a website with AI and the webpage involves placeholder images or other types of visual content, this MCP server should:

* Automatically call an image generation model through AI.
* Provide an appropriate **English prompt** based on the context.
* Generate the image and **fill it into the corresponding section of the webpage**.

### Image Generation API Usage

Below is the method for calling the text-to-image API.
**Important:** The API key is **provided by the MCP client** — **do NOT hardcode** it into the MCP server.

```python
import requests
import json
from PIL import Image
from io import BytesIO

url = 'https://api-inference.modelscope.cn/v1/images/generations'
payload = {
    'model': 'MusePublic/489_ckpt_FLUX_1',  # ModelScope Model-Id, required
    'prompt': 'A golden cat'  # Required
}
headers = {
    'Authorization': 'Bearer API_KEY',  # Passed from MCP client
    'Content-Type': 'application/json'
}

response = requests.post(
    url,
    data=json.dumps(payload, ensure_ascii=False).encode('utf-8'),
    headers=headers
)

response_data = response.json()
image = Image.open(BytesIO(requests.get(response_data['images'][0]['url']).content))
image.save('result_image.jpg')
```
